# 光电仿真系统流程讲解

本文档详细介绍光电仿真系统中三个核心模块的处理流程：光电目标数据产生、光电干扰数据产生、光电侦查数据产生。

## 系统架构概述

光电仿真系统采用模块化设计，主要包含以下核心组件：
- **仿真引擎** (`simulation_engine.py`): 协调各模块执行，管理多线程处理
- **配置管理器** (`config_manager.py`): 解析和验证配置文件
- **输出管理器** (`output_manager.py`): 管理仿真结果输出
- **物理模型** (`physics/`): 实现辐射、大气传输、探测器等物理计算
- **设备仿真器** (`devices/`): 实现各类光电设备的仿真逻辑

---

## 1. 光电目标数据产生

### 1.1 模块概述

光电目标数据产生模块主要负责仿真各类光电目标（如飞机、车辆等）的辐射特性和图像生成。核心实现在 `optical_target.py` 文件中的 `OpticalTargetSimulator` 类。

### 1.2 整体处理流程

```mermaid
graph TD
    A[配置初始化] --> B[物理模型构建]
    B --> C[传感器配置]
    C --> D[数据生成请求]
    D --> E{生成类型}
    E -->|静态图像| F[生成场景参数]
    E -->|动态视频| G[生成动态参数]
    E -->|参数数据| H[生成性能参数]
    F --> I[辐射计算]
    G --> I
    H --> J[统计分析]
    I --> K[大气传输]
    K --> L[图像渲染]
    L --> M[添加标注]
    M --> N[保存输出]
    J --> N
```

### 1.3 详细实现流程

#### 1.3.1 初始化阶段
**配置解析与模型构建**
- 根据目标类型（aircraft/vehicle）构建辐射配置，包括各组件温度分布
- 初始化物理模型：`TargetRadiation`（辐射模型）、`AtmosphericTransmission`（大气传输）、`ImagingSensor`（成像传感器）
- 设置性能参数：探测距离（默认10km）、分辨率（0.1mrad）、视场角（10度）

**辐射模型配置**
```python
# 飞机目标的温度配置示例
config = {
    'temperature': {
        'engine': 600K,     # 发动机温度
        'body': 300K,       # 机体温度
        'exhaust': 900K,    # 排气温度
        'background': 280K  # 背景温度
    },
    'component_areas': {
        'engine': 2.0,      # 发动机面积
        'body': 50.0,       # 机体面积
        'exhaust': 1.0      # 排气面积
    }
}
```

#### 1.3.2 场景参数生成
**静态场景参数**
- 目标距离：1km到探测距离范围内随机生成
- 目标角度：方位角±视场角/2，俯仰角±视场角/4
- 环境条件：天气因子（0.7-1.0），目标状态（normal/hot/cold）
- 时间戳：记录生成时间用于数据追溯

**动态场景参数**
- 目标运动：距离 = 5000 + 1000×sin(0.1t)，模拟周期性运动
- 角度变化：方位角 = 5×sin(0.2t)，俯仰角 = 2×cos(0.15t)
- 温度变化：应用周期性温度变化，模拟发动机工作状态变化

#### 1.3.3 辐射特性计算
**黑体辐射建模**
- 基于普朗克函数计算各组件的光谱辐射亮度
- 使用斯蒂芬-玻尔兹曼定律计算总辐射功率
- 维恩位移定律确定峰值波长

**组件辐射计算**
```python
# 计算组件辐射强度
def get_radiant_intensity(component, wavelength_range, area):
    temperature = self.temperature_map[component]
    emissivity = self.emissivity_map[component]

    if wavelength_range is None:
        # 全波段辐射
        radiant_exitance = stefan_boltzmann_law(temperature, emissivity)
        return radiant_exitance * area / π
    else:
        # 特定波段辐射
        spectral_radiance = spectral_radiance_band(temperature, wavelength_range)
        return spectral_radiance * emissivity * area
```

#### 1.3.4 大气传输建模
**Beer-Lambert定律应用**
- 计算大气透射率：T = exp(-β×d)
- 消光系数基于波长相关性：瑞利散射∝λ⁻⁴，米散射∝λ⁻¹

**分子吸收计算**
- 水蒸气吸收：基于湿度、温度、气压计算水蒸气密度
- CO2吸收：考虑4.3μm和15μm主要吸收带
- 总透射率：散射透射率×吸收透射率

#### 1.3.5 图像生成与渲染
**目标图像生成**
- 创建640×480基础图像，添加随机背景噪声
- 目标位置：基于方位角和俯仰角计算像素坐标
- 目标大小：基于距离的反比关系，base_size×1000/distance
- 目标亮度：结合辐射强度、大气传输、距离衰减

**热点和状态模拟**
- 正常状态：标准亮度渲染
- 热状态：添加发动机热点，亮度增加50
- 冷状态：降低整体亮度

**中文标注添加**
- 目标型号、距离、方位角、俯仰角、状态信息
- 支持多种中文字体：SimHei、Microsoft YaHei等
- 动态标注包含时间戳信息

#### 1.3.6 参数数据生成
**偏离范围数据**
- 基础偏离：分辨率×1000（转换为mrad）
- 环境影响：天气因子（0.8-1.2），距离因子（0.9-1.1）
- 方位和俯仰偏离：独立计算，总偏离为矢量和

**识别准确率数据**
- 基础准确率：0.85
- 距离影响：远距离准确率下降，最大衰减40%
- 天气影响：恶劣天气准确率降低
- 目标大小影响：小目标识别困难

**探测距离数据**
- 基础探测距离：配置的最大探测距离
- 天气修正：晴天（0.9-1.0），雾霾（0.6-0.8），大雾（0.3-0.5）
- 目标对比度：影响实际探测能力

**探测概率数据**
- 分段概率模型：
  - 近距离（<50%探测距离）：概率0.95
  - 中距离（50%-100%探测距离）：线性衰减到0.5
  - 远距离（>探测距离）：指数衰减

---

## 2. 光电干扰数据产生

### 2.1 模块概述

光电干扰数据产生模块仿真各类光电干扰设备的工作特性，包括烟幕、激光致盲、红外诱饵等。核心实现在 `optical_jammer.py` 文件中的 `OpticalJammerSimulator` 类。

### 2.2 整体处理流程

```mermaid
graph TD
    A[干扰器初始化] --> B[确定干扰类型]
    B --> C[物理模型配置]
    C --> D[干扰参数设置]
    D --> E[数据生成]
    E --> F[干扰效果计算]
    F --> G[功耗分析]
    G --> H[覆盖范围计算]
    H --> I[持续时间分析]
    I --> J[环境影响评估]
    J --> K[综合数据输出]
```

### 2.3 详细实现流程

#### 2.3.1 干扰器类型识别与初始化
**类型自动识别**
```python
def _determine_jammer_type():
    model_lower = self.config.model.lower()
    if 'smoke' in model_lower or '烟幕' in model_lower:
        return 'smoke_screen'
    elif 'laser' in model_lower or '激光' in model_lower:
        return 'laser_dazzler'
    elif 'decoy' in model_lower or '诱饵' in model_lower:
        return 'infrared_decoy'
    elif 'chaff' in model_lower or '箔条' in model_lower:
        return 'chaff'
```

**激光干扰器特殊配置**
- 激光波长：默认0.532μm（绿光）
- 光束发散角：10mrad
- 脉冲持续时间：1μs
- 重复频率：1000Hz

#### 2.3.2 干扰效果建模
**烟幕干扰效果**
- 覆盖半径内：效果 = 0.9×(1 - distance/radius×0.5)
- 覆盖半径外：指数衰减模型
- 主要影响可见光和近红外探测

**激光致盲效果**
- 基于功率密度计算：power_density = laser_power/beam_area
- 效果评估：min(0.98, power_density/10000)
- 距离平方反比衰减

**红外诱饵效果**
- 基于辐射强度：radiant_intensity/(distance² + 1000)
- 模拟热源诱骗红外探测器
- 最大效果限制为0.95

#### 2.3.3 环境影响建模
**天气对不同干扰类型的影响**
- 烟幕干扰：
  - 晴天：效果因子0.9-1.0
  - 大风：效果因子0.5-0.7（风吹散烟幕）
  - 雨天：效果因子0.6-0.8（雨水稀释）
- 激光干扰：
  - 晴天：传输因子0.95-1.0
  - 雾霾：传输因子0.7-0.8
  - 大雾：传输因子0.3-0.5

**大气传输影响**
- 激光干扰考虑大气透射率
- 使用大气传输模型计算衰减
- 其他干扰类型影响较小

#### 2.3.4 功耗分析建模
**工作模式影响**
```python
def calculate_power_consumption():
    base_power = self.jamming_power

    if work_mode == 'continuous':
        mode_factor = 1.0
    elif work_mode == 'pulse':
        duty_cycle = random.uniform(0.1, 0.5)
        mode_factor = duty_cycle
    else:
        mode_factor = random.uniform(0.3, 0.8)

    # 温度影响
    temp_factor = 1.0 + (ambient_temp - 288.15) * 0.002

    # 效率损失
    efficiency = random.uniform(0.7, 0.9)
    total_power = base_power * mode_factor * temp_factor / efficiency
```

#### 2.3.5 覆盖范围计算
**功率与覆盖关系**
- 功率因子：sqrt(jamming_power/1000)，体现功率开方关系
- 天气修正：不同天气条件的影响因子
- 地形修正：随机地形影响因子（0.8-1.2）

**覆盖角度特性**
- 激光干扰：5-15度（定向性强）
- 其他干扰：30-360度（全向或扇形覆盖）

#### 2.3.6 持续时间建模
**不同干扰类型的持续特性**
- 烟幕干扰：基础持续时间300秒，受风速影响显著
- 红外诱饵：燃烧时间60秒，相对稳定
- 激光干扰：可连续工作，受功率和散热限制

**环境影响计算**
```python
def calculate_duration():
    if jammer_type == 'smoke_screen':
        wind_factor = max(0.3, 1.0 - wind_speed * 0.1)
    else:
        wind_factor = random.uniform(0.9, 1.1)

    actual_duration = base_duration * wind_factor * power_factor
```

---

## 3. 光电侦查数据产生

### 3.1 模块概述

光电侦查数据产生模块仿真光电侦察设备的探测、识别、跟踪能力。核心实现在 `optical_recon.py` 文件中的 `OpticalReconSimulator` 类。

### 3.2 整体处理流程

```mermaid
graph TD
    A[侦察器初始化] --> B[确定侦察类型]
    B --> C[传感器配置]
    C --> D[探测器建模]
    D --> E[侦察数据生成]
    E --> F[初筛检测]
    F --> G[特征提取]
    G --> H[目标跟踪]
    H --> I[识别分析]
    I --> J[探测距离评估]
    J --> K[发现概率计算]
    K --> L[综合数据输出]
```

### 3.3 详细实现流程

#### 3.3.1 侦察器类型识别与配置
**类型自动识别**
```python
def _determine_recon_type():
    model_lower = self.config.model.lower()
    detection_mode = self.config.detection_mode.lower()

    if 'infrared' in model_lower or 'ir' in model_lower:
        return 'infrared_detector'
    elif 'laser' in model_lower:
        return 'laser_warning'
    elif 'optical' in model_lower:
        return 'electro_optical'
    elif 'spectrum' in model_lower:
        return 'spectral_analyzer'
```

**传感器配置**
- 红外探测器：中红外波段（3-5μm），量子效率0.7，像素20μm
- 激光告警：近红外波段（1.0-1.7μm），量子效率0.85，像素15μm
- 光电探测：可见光波段（0.4-0.7μm），量子效率0.9，像素5μm

**探测器建模**
- 基于物理参数构建PhotoDetector模型
- 计算响应度、噪声电流、信噪比
- 探测率D*计算

#### 3.3.2 初筛检测建模
**目标存在性仿真**
- 目标出现概率：30%（模拟实际侦察场景）
- 信号强度：目标存在时0.5-1.0，不存在时0.0-0.4
- 噪声水平：0.05-0.2随机分布

**检测判决**
```python
def initial_screening():
    snr = signal_strength / noise_level
    detected = snr > detection_threshold  # 默认阈值0.7

    if target_present and not detected:
        result_type = 'miss'          # 漏检
    elif not target_present and detected:
        result_type = 'false_alarm'   # 虚警
    elif target_present and detected:
        result_type = 'hit'           # 命中
    else:
        result_type = 'correct_rejection'  # 正确拒绝
```

#### 3.3.3 特征提取建模
**特征类型与质量**
- 光谱特征：质量0.6-0.95，基于光谱分辨率
- 空间特征：质量0.7-0.9，基于图像分辨率
- 时间特征：质量0.5-0.8，基于时间分辨率
- 偏振特征：质量0.4-0.7，技术难度较高

**特征提取过程**
```python
def feature_extraction():
    feature_types = ['spectral', 'spatial', 'temporal', 'polarization']
    extracted_features = random.choice(feature_types, size=random.randint(1, 4))

    feature_quality = {}
    for feature_type in extracted_features:
        quality = calculate_feature_quality(feature_type)
        feature_quality[feature_type] = quality

    overall_confidence = mean(feature_quality.values())
    processing_time = random.uniform(0.1, 2.0)  # 处理时间
```

#### 3.3.4 目标跟踪建模
**运动参数生成**
- 目标速度：10-300 m/s，覆盖各类目标
- 运动方向：0-360度随机分布
- 跟踪精度：基础精度0.9，受速度和距离影响

**跟踪精度计算**
```python
def tracking_accuracy():
    base_accuracy = 0.9
    speed_factor = max(0.5, 1.0 - (target_speed - 10) / 290 * 0.3)
    distance_factor = random.uniform(0.8, 1.0)

    tracking_accuracy = base_accuracy * speed_factor * distance_factor
    tracking_accuracy = max(0.3, min(0.99, tracking_accuracy))
```

**跟踪状态管理**
- 捕获状态（acquiring）：10%概率
- 跟踪状态（tracking）：70%概率
- 丢失状态（lost）：10%概率
- 惯性跟踪（coasting）：10%概率

#### 3.3.5 识别分析建模
**目标分类**
- 目标类型：aircraft, missile, vehicle, ship, unknown
- 识别置信度：正确识别0.7-0.95，错误识别0.3-0.8

**影响因子建模**
```python
def recognition_accuracy():
    # 距离影响
    distance_factor = max(0.4, 1.0 - (distance - 1000) / (detection_range - 1000) * 0.5)

    # 天气影响
    weather_factor = random.uniform(0.7, 1.0)

    # 目标大小影响
    target_size_factor = random.uniform(0.5, 1.5)

    # 最终准确率
    recognition_accuracy = confidence * distance_factor * weather_factor
```

#### 3.3.6 探测能力评估
**探测距离建模**
- 基础探测距离：配置的最大探测距离（默认15km）
- 天气修正：晴天0.9-1.0，雾霾0.6-0.8，大雾0.3-0.5
- 目标特征：目标信号强度0.3-1.5
- 传感器性能：设备状态0.8-1.1

**发现概率建模**
```python
def discovery_probability():
    if distance <= detection_range * 0.3:
        base_probability = 0.98      # 近距离高概率
    elif distance <= detection_range * 0.7:
        # 中距离线性衰减
        base_probability = 0.9 - 0.3 * (distance - detection_range * 0.3) / (detection_range * 0.4)
    elif distance <= detection_range:
        # 远距离快速衰减
        base_probability = 0.6 - 0.4 * (distance - detection_range * 0.7) / (detection_range * 0.3)
    else:
        # 超远距离指数衰减
        base_probability = 0.2 * exp(-(distance - detection_range) / (detection_range * 0.5))
```

---

## 系统集成与协调

### 仿真引擎协调机制
**多线程并行处理**
```python
with ThreadPoolExecutor(max_workers=num_threads) as executor:
    futures = []

    # 提交各类设备仿真任务
    for i, simulator in enumerate(target_simulators):
        future = executor.submit(run_target_simulation, simulator, i)
        futures.append(('target', i, future))

    # 收集结果
    for device_type, device_id, future in futures:
        device_results = future.result()
        merge_results(results, device_results)
```

**干扰效果分析**
- 计算目标与干扰设备间距离
- 基于距离和功率计算干扰比
- 生成综合干扰效果分析报告

### 数据输出管理
**会话隔离机制**
- 每次运行创建时间戳目录：session_YYYYMMDD_HHMMSS_fff
- 子目录分类：images/、videos/、data/、logs/、configs/
- 文件命名规范：设备类型_设备ID_数据类型_序号

**数据格式标准化**
- JSON格式结构化数据输出
- 设备信息、性能参数、统计数据分层组织
- 时间戳记录确保数据可追溯性
- URL编码支持中文路径

### 物理模型集成
**辐射-传输-探测链路**
1. 目标辐射特性计算（TargetRadiation）
2. 大气传输效应建模（AtmosphericTransmission）
3. 探测器响应仿真（PhotoDetector/ImagingSensor）
4. 信号处理与图像生成

**环境一致性**
- 所有模块共享环境参数（温度、湿度、能见度等）
- 天气条件对各模块的一致性影响
- 时间同步确保动态仿真的协调性

## 总结

光电仿真系统通过严格的物理建模和数学计算，实现了高保真的光电对抗场景仿真。三个核心模块各司其职又相互协调：

1. **光电目标模块**：基于黑体辐射理论和大气传输模型，生成真实的目标图像和性能参数
2. **光电干扰模块**：模拟多种干扰机制，考虑环境影响和设备特性，评估干扰效果
3. **光电侦察模块**：仿真完整的探测-识别-跟踪流程，提供全面的侦察能力评估

系统支持多线程并行处理、会话隔离、多格式输出，确保了仿真效率和结果的可靠性。